I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I analyzed both the simple and advanced versions of the input handler and found that the simple version lacks comprehensive error handling, proper resource management, and structured logging. The current implementation has basic try-catch coverage but console.log/error calls throughout, minimal graceful degradation for UI spawn failures, and could benefit from better timeout handling with user feedback. The advanced version demonstrates best practices with robust error handling, centralized cleanup, and structured logging using Pino.

### Approach

I'll enhance the error handling by implementing comprehensive try-catch blocks around all async operations, improve resource cleanup with proper Promise.allSettled usage, add graceful degradation for UI spawn failures, and replace all console logging with structured logger calls. The approach focuses on making the system more resilient while maintaining the existing API contract, particularly preserving the "__TIMEOUT__" sentinel value that the MCP server expects.

### Reasoning

I examined the repository structure and read both versions of the input handler to understand the differences. I analyzed the current package.json and constants, checked how getCmdWindowInput is called in index.ts to understand error handling expectations, searched for all console.log/error calls throughout the codebase, and verified that the UI script properly writes heartbeat files. This gave me complete understanding of the integration points and requirements.

## Mermaid Diagram

sequenceDiagram
    participant Caller as MCP Server
    participant Handler as Input Handler
    participant Logger as Structured Logger
    participant Spawn as UI Process
    participant Cleanup as Resource Cleanup
    participant FileSystem as Temp Files

    Caller->>Handler: getCmdWindowInput()
    Handler->>Logger: logger.debug('Starting input session')
    
    Handler->>Handler: Generate sessionId & temp paths
    Handler->>FileSystem: Write options file
    
    alt UI Spawn Success
        Handler->>Spawn: spawn UI process
        Handler->>Logger: logger.debug('UI process spawned')
        Spawn->>FileSystem: Write heartbeat file
        Spawn->>FileSystem: Write response file
        Handler->>Handler: Monitor heartbeat & response
        Handler->>Cleanup: cleanupAndResolve(response)
    else UI Spawn Failure
        Handler->>Logger: logger.error('UI spawn failed')
        Handler->>Cleanup: cleanupAndResolve('__TIMEOUT__')
    end
    
    alt Timeout Reached
        Handler->>Logger: logger.warn('Input timeout reached')
        Handler->>Cleanup: cleanupAndResolve('__TIMEOUT__')
    else Heartbeat Lost
        Handler->>Logger: logger.debug('Heartbeat lost')
        Handler->>Cleanup: cleanupAndResolve('__TIMEOUT__')
    else File Read Error
        Handler->>Logger: logger.error('File read error')
        Handler->>Cleanup: cleanupAndResolve('')
    end
    
    Cleanup->>FileSystem: Promise.allSettled cleanup
    Cleanup->>Logger: logger.debug('Resources cleaned')
    Cleanup->>Caller: Return result
    
    Note over Logger: All console.log/error replaced
    Note over Cleanup: Graceful degradation on all failures

## Proposed File Changes

### simple-interactive-mcp\src\input-handler.ts(MODIFY)

References: 

- interactive-mcp\src\commands\input\index.ts
- simple-interactive-mcp\src\utils\logger.ts

Add structured logging import at the top: `import logger from './utils/logger.js';`. Enhance the main try-catch block to wrap the entire async IIFE with comprehensive error handling. Replace all 8 console.log/error calls with appropriate logger methods: use `logger.error()` for actual errors (lines 189, 220, 224, 240), `logger.debug()` for heartbeat monitoring messages (lines 203, 213, 216) since they are debugging information, and `logger.warn()` for the timeout message (line 234) since it's a warning condition. Improve the spawn error handling by adding more specific error messages and ensuring the UI spawn failure path properly calls cleanupAndResolve with '__TIMEOUT__' to maintain API contract. Enhance the cleanupResources function to use Promise.allSettled for better error resilience and add logging for cleanup operations. Add better error context in catch blocks to include sessionId and operation details for easier debugging.

### simple-interactive-mcp\src\index.ts(MODIFY)

References: 

- simple-interactive-mcp\src\utils\logger.ts

Add structured logging import at the top: `import logger from './utils/logger.js';`. Replace the 4 console.error calls with appropriate logger methods: use `logger.error()` for uncaught exceptions and unhandled rejections (lines 72, 77, 89), and `logger.info()` for the successful startup message (line 85) since it's informational rather than an error. Enhance the error handling in the request_user_input tool handler to provide more detailed error context and ensure proper error propagation. The startup message should be moved from console.error to logger.info since it's not actually an error condition.