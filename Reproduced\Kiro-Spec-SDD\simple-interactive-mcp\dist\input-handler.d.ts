/**
 * Display a command window with a prompt and return user input
 * @param projectName Name of the project requesting input (used for title)
 * @param promptMessage Message to display to the user
 * @param timeoutSeconds Timeout in seconds
 * @param showCountdown Whether to show a countdown timer
 * @param predefinedOptions Optional list of predefined options for quick selection
 * @returns User input or '__TIMEOUT__' if timeout
 */
export declare function getCmdWindowInput(projectName: string, promptMessage: string, timeoutSeconds?: number, showCountdown?: boolean, predefinedOptions?: string[]): Promise<string>;
