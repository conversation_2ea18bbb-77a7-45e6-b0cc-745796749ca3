import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import fsPromises from 'fs/promises';
import { watch } from 'fs';
import os from 'os';
import crypto from 'crypto';
import { USER_INPUT_TIMEOUT_SECONDS } from './constants.js';
// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));
// Define cleanupResources outside the promise to be accessible in the final catch
async function cleanupResources(heartbeatPath, responsePath, optionsPath) {
    await Promise.allSettled([
        fsPromises.unlink(responsePath).catch(() => { }),
        fsPromises.unlink(heartbeatPath).catch(() => { }),
        fsPromises.unlink(optionsPath).catch(() => { }),
    ]);
}
/**
 * Display a command window with a prompt and return user input
 * @param projectName Name of the project requesting input (used for title)
 * @param promptMessage Message to display to the user
 * @param timeoutSeconds Timeout in seconds
 * @param showCountdown Whether to show a countdown timer
 * @param predefinedOptions Optional list of predefined options for quick selection
 * @returns User input or '__TIMEOUT__' if timeout
 */
export async function getCmdWindowInput(projectName, promptMessage, timeoutSeconds = USER_INPUT_TIMEOUT_SECONDS, showCountdown = true, predefinedOptions) {
    // Create a temporary file for the detached process to write to
    const sessionId = crypto.randomBytes(8).toString('hex');
    const tempDir = os.tmpdir();
    const tempFilePath = path.join(tempDir, `cmd-ui-response-${sessionId}.txt`);
    const heartbeatFilePath = path.join(tempDir, `cmd-ui-heartbeat-${sessionId}.txt`);
    const optionsFilePath = path.join(tempDir, `cmd-ui-options-${sessionId}.json`);
    return new Promise((resolve) => {
        // Wrap the async setup logic in an IIFE
        void (async () => {
            // Path to the UI script (will be in the same directory after compilation)
            const uiScriptPath = path.join(__dirname, 'ui.js');
            // Gather options
            const options = {
                projectName,
                prompt: promptMessage,
                timeout: timeoutSeconds,
                showCountdown,
                sessionId,
                outputFile: tempFilePath,
                heartbeatFile: heartbeatFilePath,
                predefinedOptions,
            };
            let ui;
            // Moved setup into try block
            try {
                // Write options to the file before spawning
                await fsPromises.writeFile(optionsFilePath, JSON.stringify(options), 'utf8');
                // Platform-specific spawning
                const platform = os.platform();
                if (platform === 'darwin') {
                    // macOS
                    const escapedScriptPath = uiScriptPath;
                    const escapedSessionId = sessionId;
                    const nodeCommand = `exec node "${escapedScriptPath}" "${escapedSessionId}" "${tempDir}"; exit 0`;
                    const escapedNodeCommand = nodeCommand
                        .replace(/\\/g, '\\\\')
                        .replace(/"/g, '\\"');
                    const command = `osascript -e 'tell application "Terminal" to activate' -e 'tell application "Terminal" to do script "${escapedNodeCommand}"'`;
                    const commandArgs = [];
                    ui = spawn(command, commandArgs, {
                        stdio: ['ignore', 'ignore', 'ignore'],
                        shell: true,
                        detached: true,
                    });
                }
                else if (platform === 'win32') {
                    // Windows
                    ui = spawn('node', [uiScriptPath, sessionId], {
                        stdio: ['ignore', 'ignore', 'ignore'],
                        shell: true,
                        detached: true,
                        windowsHide: false,
                    });
                }
                else {
                    // Linux or other
                    ui = spawn('node', [uiScriptPath, sessionId], {
                        stdio: ['ignore', 'ignore', 'ignore'],
                        shell: true,
                        detached: true,
                    });
                }
                let watcher = null;
                let timeoutHandle = null;
                let heartbeatInterval = null;
                let heartbeatFileSeen = false;
                const startTime = Date.now();
                // Define cleanupAndResolve inside the promise scope
                const cleanupAndResolve = async (response) => {
                    if (heartbeatInterval) {
                        clearInterval(heartbeatInterval);
                        heartbeatInterval = null;
                    }
                    if (watcher) {
                        watcher.close();
                        watcher = null;
                    }
                    if (timeoutHandle) {
                        clearTimeout(timeoutHandle);
                        timeoutHandle = null;
                    }
                    await cleanupResources(heartbeatFilePath, tempFilePath, optionsFilePath);
                    resolve(response);
                };
                // Listen for process exit events
                const handleExit = (code) => {
                    if (code !== 0 && (watcher || timeoutHandle)) {
                        void cleanupAndResolve('');
                    }
                };
                const handleError = () => {
                    if (watcher || timeoutHandle) {
                        void cleanupAndResolve('');
                    }
                };
                ui.on('exit', handleExit);
                ui.on('error', handleError);
                // Unref the child process so the parent can exit independently
                ui.unref();
                // Create an empty temp file before watching for user response
                await fsPromises.writeFile(tempFilePath, '', 'utf8');
                // Wait briefly for the heartbeat file to potentially be created
                await new Promise((res) => setTimeout(res, 500));
                // Watch for content being written to the temp file
                watcher = watch(tempFilePath, (eventType) => {
                    if (eventType === 'change') {
                        void (async () => {
                            try {
                                const data = await fsPromises.readFile(tempFilePath, 'utf8');
                                if (data) {
                                    const response = data.trim();
                                    void cleanupAndResolve(response);
                                }
                            }
                            catch (readError) {
                                console.error('Error reading response file:', readError);
                                void cleanupAndResolve('');
                            }
                        })();
                    }
                });
                // Start heartbeat check interval
                heartbeatInterval = setInterval(() => {
                    void (async () => {
                        try {
                            const stats = await fsPromises.stat(heartbeatFilePath);
                            const now = Date.now();
                            if (now - stats.mtime.getTime() > 3000) {
                                console.log('Heartbeat file not updated recently. Process likely exited.');
                                void cleanupAndResolve('__TIMEOUT__');
                            }
                            else {
                                heartbeatFileSeen = true;
                            }
                        }
                        catch (err) {
                            if (err && typeof err === 'object' && 'code' in err) {
                                const error = err;
                                if (error.code === 'ENOENT') {
                                    if (heartbeatFileSeen) {
                                        console.log('Heartbeat file not found after being seen. Process likely exited.');
                                        void cleanupAndResolve('__TIMEOUT__');
                                    }
                                    else if (Date.now() - startTime > 7000) {
                                        console.log('Heartbeat file never appeared. Process likely failed to start.');
                                        void cleanupAndResolve('__TIMEOUT__');
                                    }
                                }
                                else {
                                    console.error('Heartbeat check error:', error);
                                    void cleanupAndResolve('__TIMEOUT__');
                                }
                            }
                            else {
                                console.error('Unexpected heartbeat check error:', err);
                                void cleanupAndResolve('__TIMEOUT__');
                            }
                        }
                    })();
                }, 1500);
                // Timeout to stop watching if no response within limit
                timeoutHandle = setTimeout(() => {
                    console.log(`Input timeout reached after ${timeoutSeconds} seconds.`);
                    void cleanupAndResolve('__TIMEOUT__');
                }, timeoutSeconds * 1000 + 5000);
            }
            catch (setupError) {
                console.error('Error during cmd-input setup:', setupError);
                await cleanupResources(heartbeatFilePath, tempFilePath, optionsFilePath);
                resolve('__TIMEOUT__');
            }
        })();
    });
}
