import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { TextInput } from '@inkjs/ui';
export const InteractiveInput = ({ question, questionId, predefinedOptions = [], onSubmit, }) => {
    const [mode, setMode] = useState(predefinedOptions.length > 0 ? 'option' : 'input');
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [inputValue, setInputValue] = useState('');
    useInput((input, key) => {
        if (predefinedOptions.length > 0) {
            if (key.upArrow) {
                setMode('option');
                setSelectedIndex((prev) => (prev - 1 + predefinedOptions.length) % predefinedOptions.length);
                return;
            }
            if (key.downArrow) {
                setMode('option');
                setSelectedIndex((prev) => (prev + 1) % predefinedOptions.length);
                return;
            }
        }
        if (key.return) {
            if (mode === 'option' && predefinedOptions.length > 0) {
                onSubmit(questionId, predefinedOptions[selectedIndex]);
            }
            else {
                onSubmit(questionId, inputValue);
            }
            return;
        }
        // Any other key press switches to input mode
        if (!key.ctrl &&
            !key.meta &&
            !key.escape &&
            !key.tab &&
            !key.shift &&
            !key.leftArrow &&
            !key.rightArrow &&
            input) {
            setMode('input');
            // Update inputValue only if switching to input mode via typing
            // TextInput's onChange will handle subsequent typing
            if (mode === 'option') {
                setInputValue(input); // Start input with the typed character
            }
        }
    });
    const handleInputChange = (value) => {
        if (value !== inputValue) {
            setInputValue(value);
            // If user starts typing, switch to input mode
            if (value.length > 0 && mode === 'option') {
                setMode('input');
            }
            else if (value.length === 0 && predefinedOptions.length > 0) {
                // Optionally switch back to option mode if input is cleared
                // setMode('option');
            }
        }
    };
    const handleSubmit = (value) => {
        // The primary submit logic is now handled in useInput via Enter key
        // This might still be called by TextInput's internal onSubmit, ensure consistency
        if (mode === 'option' && predefinedOptions.length > 0) {
            onSubmit(questionId, predefinedOptions[selectedIndex]);
        }
        else {
            onSubmit(questionId, value); // Use the value from TextInput in case it triggered submit
        }
    };
    return (_jsxs(_Fragment, { children: [_jsx(Box, { flexDirection: "column", marginBottom: 1, children: _jsx(Text, { bold: true, color: "cyan", wrap: "wrap", children: question }) }), predefinedOptions.length > 0 && (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsx(Text, { dimColor: true, children: "Use \u2191/\u2193 to select options, type for custom input, Enter to submit" }), predefinedOptions.map((opt, i) => (_jsxs(Text, { color: i === selectedIndex && mode === 'option'
                            ? 'greenBright'
                            : undefined, children: [i === selectedIndex && mode === 'option' ? '› ' : '  ', opt] }, i)))] })), _jsx(Box, { children: _jsxs(Text, { color: mode === 'input' ? 'greenBright' : undefined, children: [mode === 'input' ? '✎ ' : '› ', _jsx(TextInput, { placeholder: predefinedOptions.length > 0
                                ? 'Type or select an option...'
                                : 'Type your answer...', onChange: handleInputChange, onSubmit: handleSubmit })] }) })] }));
};
