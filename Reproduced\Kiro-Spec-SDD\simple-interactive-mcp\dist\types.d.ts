import { z } from 'zod';
export declare const requestUserInputSchema: z.ZodObject<{
    projectName: z.ZodString;
    message: z.ZodString;
    predefinedOptions: z.ZodOptional<z.<PERSON>od<PERSON><PERSON>y<z.ZodString, "many">>;
}, "strip", z.<PERSON>ny, {
    projectName: string;
    message: string;
    predefinedOptions?: string[] | undefined;
}, {
    projectName: string;
    message: string;
    predefinedOptions?: string[] | undefined;
}>;
export type RequestUserInputArgs = z.infer<typeof requestUserInputSchema>;
export interface UIOptions {
    projectName: string;
    prompt: string;
    timeout: number;
    sessionId: string;
    outputFile: string;
    heartbeatFile: string;
    predefinedOptions?: string[];
}
