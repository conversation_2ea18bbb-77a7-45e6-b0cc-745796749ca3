import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { render, Box, Text, useApp } from 'ink';
import { ProgressBar } from '@inkjs/ui';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import { InteractiveInput } from './interactive-input.js';
// Define defaults separately
const defaultOptions = {
    prompt: 'Enter your response:',
    timeout: 30,
    showCountdown: false,
    projectName: undefined,
    predefinedOptions: undefined,
};
// Function to read options from the file specified by sessionId
const readOptionsFromFile = async () => {
    const args = process.argv.slice(2);
    const sessionId = args[0];
    if (!sessionId) {
        console.error('No sessionId provided. Exiting.');
        throw new Error('No sessionId provided');
    }
    let tempDir = args[1];
    if (!tempDir) {
        tempDir = os.tmpdir();
    }
    const optionsFilePath = path.join(tempDir, `cmd-ui-options-${sessionId}.json`);
    try {
        const optionsData = await fs.readFile(optionsFilePath, 'utf8');
        const parsedOptions = JSON.parse(optionsData);
        // Validate required fields after parsing
        if (!parsedOptions.sessionId ||
            !parsedOptions.outputFile ||
            !parsedOptions.heartbeatFile) {
            throw new Error('Required options missing in options file.');
        }
        // Merge defaults with parsed options, ensuring required fields are fully typed
        return {
            ...defaultOptions,
            ...parsedOptions,
            sessionId: parsedOptions.sessionId,
            outputFile: parsedOptions.outputFile,
            heartbeatFile: parsedOptions.heartbeatFile,
        };
    }
    catch (error) {
        console.error(`Failed to read or parse options file ${optionsFilePath}:`, error instanceof Error ? error.message : error);
        throw error;
    }
};
// Function to write response to output file if provided
const writeResponseToFile = async (outputFile, response) => {
    if (!outputFile)
        return;
    await fs.writeFile(outputFile, response, 'utf8');
};
// Global state for options and exit handler setup
let options = null;
let exitHandlerAttached = false;
// Async function to initialize options and setup exit handlers
async function initialize() {
    try {
        options = await readOptionsFromFile();
        // Setup exit handlers only once after options are successfully read
        if (!exitHandlerAttached) {
            const handleExit = () => {
                if (options && options.outputFile) {
                    // Write empty string to indicate abnormal exit (e.g., Ctrl+C)
                    writeResponseToFile(options.outputFile, '')
                        .catch((error) => {
                        console.error('Failed to write exit file:', error);
                    })
                        .finally(() => process.exit(0));
                }
                else {
                    process.exit(0);
                }
            };
            process.on('SIGINT', handleExit);
            process.on('SIGTERM', handleExit);
            process.on('beforeExit', handleExit);
            exitHandlerAttached = true;
        }
    }
    catch (error) {
        console.error('Initialization failed:', error);
        process.exit(1);
    }
}
const App = ({ options: appOptions }) => {
    const { exit } = useApp();
    const { projectName, prompt, timeout, showCountdown, outputFile, heartbeatFile, predefinedOptions, } = appOptions;
    const [timeLeft, setTimeLeft] = useState(timeout);
    // Clear console only once on mount
    useEffect(() => {
        console.clear();
    }, []);
    // Handle countdown and auto-exit on timeout
    useEffect(() => {
        const timer = setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    clearInterval(timer);
                    writeResponseToFile(outputFile, '__TIMEOUT__')
                        .catch((err) => console.error('Failed to write timeout file:', err))
                        .finally(() => exit());
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);
        // Add heartbeat interval
        let heartbeatInterval;
        if (heartbeatFile) {
            heartbeatInterval = setInterval(async () => {
                try {
                    // Touch the file (create if not exists, update mtime if exists)
                    const now = new Date();
                    await fs.utimes(heartbeatFile, now, now);
                }
                catch (err) {
                    // If file doesn't exist, try to create it
                    if (err &&
                        typeof err === 'object' &&
                        'code' in err &&
                        err.code === 'ENOENT') {
                        try {
                            await fs.writeFile(heartbeatFile, '', 'utf8');
                        }
                        catch (createErr) {
                            // Ignore errors creating heartbeat file
                        }
                    }
                }
            }, 1000);
        }
        return () => {
            clearInterval(timer);
            if (heartbeatInterval) {
                clearInterval(heartbeatInterval);
            }
        };
    }, [exit, outputFile, heartbeatFile, timeout]);
    // Handle final submission
    const handleSubmit = (value) => {
        console.log(`User submitted: ${value}`);
        writeResponseToFile(outputFile, value)
            .catch((err) => console.error('Failed to write response file:', err))
            .finally(() => {
            exit();
        });
    };
    // Wrapper for handleSubmit to match the signature of InteractiveInput's onSubmit
    const handleInputSubmit = (_questionId, value) => {
        handleSubmit(value);
    };
    const progressValue = (timeLeft / timeout) * 100;
    return (_jsxs(Box, { flexDirection: "column", padding: 1, borderStyle: "round", borderColor: "blue", children: [projectName && (_jsx(Box, { marginBottom: 1, justifyContent: "center", children: _jsx(Text, { bold: true, color: "magenta", children: projectName }) })), _jsx(InteractiveInput, { question: prompt, questionId: prompt, predefinedOptions: predefinedOptions, onSubmit: handleInputSubmit }), showCountdown && (_jsxs(Box, { flexDirection: "column", marginTop: 1, children: [_jsxs(Text, { color: "yellow", children: ["Time remaining: ", timeLeft, "s"] }), _jsx(ProgressBar, { value: progressValue })] }))] }));
};
// Initialize and render the app
initialize()
    .then(() => {
    if (options) {
        render(_jsx(App, { options: options }));
    }
    else {
        console.error('Options could not be initialized. Cannot render App.');
        process.exit(1);
    }
})
    .catch(() => {
    process.exit(1);
});
