{"name": "simple-interactive-mcp", "version": "1.0.0", "type": "module", "main": "dist/index.js", "bin": {"simple-interactive-mcp": "dist/index.js"}, "scripts": {"build": "tsc --outDir dist && tsc-alias", "start": "node dist/index.js", "dev": "tsc --watch --outDir dist", "lint": "eslint src/**/*.{ts,tsx}", "format": "prettier --write src/**/*.{ts,tsx}"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.10.2", "@inkjs/ui": "^2.0.0", "ink": "^5.2.0", "pino": "^9.6.0", "react": "^18.3.1", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.15.2", "@types/pino": "^7.0.5", "@types/react": "^19.1.2", "typescript": "^5.8.3", "eslint": "^9.25.1", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "tsc-alias": "^1.8.15"}}