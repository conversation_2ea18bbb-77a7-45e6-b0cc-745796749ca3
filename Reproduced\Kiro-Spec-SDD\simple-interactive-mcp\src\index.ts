#!/usr/bin/env node
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { requestUserInputSchema, RequestUserInputArgs } from './types.js';
import { getCmdWindowInput } from './input-handler.js';
import { USER_INPUT_TIMEOUT_SECONDS, APP_NAME, APP_VERSION } from './constants.js';
import logger from './utils/logger.js';

// Initialize MCP server with only request_user_input tool
const server = new McpServer({
  name: APP_NAME,
  version: APP_VERSION,
  capabilities: {
    tools: {
      request_user_input: {
        description: `Ask the user for input with an optional timeout (default: ${USER_INPUT_TIMEOUT_SECONDS}s). Supports predefined options for quick selection.`,
        inputSchema: requestUserInputSchema,
      },
    },
  },
});

// Register the single request_user_input tool
server.tool(
  'request_user_input',
  `Ask the user for input with an optional timeout (default: ${USER_INPUT_TIMEOUT_SECONDS}s). Supports predefined options for quick selection.`,
  requestUserInputSchema,
  async (args: RequestUserInputArgs) => {
    const { projectName, message, predefinedOptions } = args;
    const promptMessage = `${projectName}: ${message}`;
    
    try {
      const answer = await getCmdWindowInput(
        projectName,
        promptMessage,
        USER_INPUT_TIMEOUT_SECONDS,
        true,
        predefinedOptions,
      );

      // Check for the specific timeout indicator
      if (answer === '__TIMEOUT__') {
        return {
          content: [
            { type: 'text', text: 'User did not reply: Timeout occurred.' },
          ],
        };
      }
      // Empty string means user submitted empty input, non-empty is actual reply
      else if (answer === '') {
        return {
          content: [{ type: 'text', text: 'User replied with empty input.' }],
        };
      } else {
        const reply = `User replied: ${answer}`;
        return { content: [{ type: 'text', text: reply }] };
      }
    } catch (error) {
      return {
        content: [
          { 
            type: 'text', 
            text: `Error getting user input: ${error instanceof Error ? error.message : String(error)}` 
          },
        ],
      };
    }
  },
);

// Error handling
process.on('uncaughtException', (error) => {
  logger.error('Exception non interceptée :', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Rejet non géré à :', promise, 'raison :', reason);
  process.exit(1);
});

// Start the server
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  logger.info(`${APP_NAME} v${APP_VERSION} démarré avec succès`);
}

main().catch((error) => {
  logger.error('Échec du démarrage du serveur :', error);
  process.exit(1);
});
