import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import fsPromises from 'fs/promises';
import { watch, FSWatcher } from 'fs';
import os from 'os';
import crypto from 'crypto';
import { USER_INPUT_TIMEOUT_SECONDS } from './constants.js';
import logger from './utils/logger.js';

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Define cleanupResources outside the promise to be accessible in the final catch
async function cleanupResources(
  heartbeatPath: string,
  responsePath: string,
  optionsPath: string,
  sessionId?: string,
) {
  try {
    const results = await Promise.allSettled([
      fsPromises.unlink(responsePath).catch(() => {}),
      fsPromises.unlink(heartbeatPath).catch(() => {}),
      fsPromises.unlink(optionsPath).catch(() => {}),
    ]);

    logger.debug(`Ressources nettoyées pour la session ${sessionId || 'inconnue'}`);

    // Log any cleanup failures for debugging
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        const paths = [responsePath, heartbeatPath, optionsPath];
        logger.debug(`Échec du nettoyage du fichier ${paths[index]}: ${result.reason}`);
      }
    });
  } catch (error) {
    logger.error(`Erreur lors du nettoyage des ressources pour la session ${sessionId || 'inconnue'}:`, error);
  }
}

/**
 * Display a command window with a prompt and return user input
 * @param projectName Name of the project requesting input (used for title)
 * @param promptMessage Message to display to the user
 * @param timeoutSeconds Timeout in seconds
 * @param showCountdown Whether to show a countdown timer
 * @param predefinedOptions Optional list of predefined options for quick selection
 * @returns User input or '__TIMEOUT__' if timeout
 */
export async function getCmdWindowInput(
  projectName: string,
  promptMessage: string,
  timeoutSeconds: number = USER_INPUT_TIMEOUT_SECONDS,
  showCountdown: boolean = true,
  predefinedOptions?: string[],
): Promise<string> {
  // Create a temporary file for the detached process to write to
  const sessionId = crypto.randomBytes(8).toString('hex');
  const tempDir = os.tmpdir();
  const tempFilePath = path.join(tempDir, `cmd-ui-response-${sessionId}.txt`);
  const heartbeatFilePath = path.join(
    tempDir,
    `cmd-ui-heartbeat-${sessionId}.txt`,
  );
  const optionsFilePath = path.join(
    tempDir,
    `cmd-ui-options-${sessionId}.json`,
  );

  return new Promise<string>((resolve) => {
    // Wrap the async setup logic in an IIFE
    void (async () => {
      try {
        logger.debug(`Démarrage de la session d'entrée ${sessionId}`);

        // Path to the UI script (will be in the same directory after compilation)
        const uiScriptPath = path.join(__dirname, 'ui.js');

      // Gather options
      const options = {
        projectName,
        prompt: promptMessage,
        timeout: timeoutSeconds,
        showCountdown,
        sessionId,
        outputFile: tempFilePath,
        heartbeatFile: heartbeatFilePath,
        predefinedOptions,
      };

      let ui;

      // Moved setup into try block
      try {
        // Write options to the file before spawning
        await fsPromises.writeFile(
          optionsFilePath,
          JSON.stringify(options),
          'utf8',
        );

        // Platform-specific spawning
        const platform = os.platform();

        if (platform === 'darwin') {
          // macOS
          const escapedScriptPath = uiScriptPath;
          const escapedSessionId = sessionId;

          const nodeCommand = `exec node "${escapedScriptPath}" "${escapedSessionId}" "${tempDir}"; exit 0`;

          const escapedNodeCommand = nodeCommand
            .replace(/\\/g, '\\\\')
            .replace(/"/g, '\\"');

          const command = `osascript -e 'tell application "Terminal" to activate' -e 'tell application "Terminal" to do script "${escapedNodeCommand}"'`;
          const commandArgs: string[] = [];

          ui = spawn(command, commandArgs, {
            stdio: ['ignore', 'ignore', 'ignore'],
            shell: true,
            detached: true,
          });
        } else if (platform === 'win32') {
          // Windows
          ui = spawn('node', [uiScriptPath, sessionId], {
            stdio: ['ignore', 'ignore', 'ignore'],
            shell: true,
            detached: true,
            windowsHide: false,
          });
        } else {
          // Linux or other
          ui = spawn('node', [uiScriptPath, sessionId], {
            stdio: ['ignore', 'ignore', 'ignore'],
            shell: true,
            detached: true,
          });
        }

        let watcher: FSWatcher | null = null;
        let timeoutHandle: NodeJS.Timeout | null = null;
        let heartbeatInterval: NodeJS.Timeout | null = null;
        let heartbeatFileSeen = false;
        const startTime = Date.now();

        // Define cleanupAndResolve inside the promise scope
        const cleanupAndResolve = async (response: string) => {
          if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
            heartbeatInterval = null;
          }
          if (watcher) {
            watcher.close();
            watcher = null;
          }
          if (timeoutHandle) {
            clearTimeout(timeoutHandle);
            timeoutHandle = null;
          }

          await cleanupResources(
            heartbeatFilePath,
            tempFilePath,
            optionsFilePath,
            sessionId,
          );

          resolve(response);
        };

        // Listen for process exit events
        const handleExit = (code?: number | null) => {
          if (code !== 0 && (watcher || timeoutHandle)) {
            logger.debug(`Processus UI terminé avec le code ${code} pour la session ${sessionId}`);
            void cleanupAndResolve('__TIMEOUT__');
          }
        };

        const handleError = (error: Error) => {
          if (watcher || timeoutHandle) {
            logger.error(`Erreur du processus UI pour la session ${sessionId}:`, error);
            void cleanupAndResolve('__TIMEOUT__');
          }
        };

        ui.on('exit', handleExit);
        ui.on('error', handleError);

        logger.debug(`Processus UI lancé pour la session ${sessionId}`);

        // Unref the child process so the parent can exit independently
        ui.unref();

        // Create an empty temp file before watching for user response
        await fsPromises.writeFile(tempFilePath, '', 'utf8');

        // Wait briefly for the heartbeat file to potentially be created
        await new Promise((res) => setTimeout(res, 500));

        // Watch for content being written to the temp file
        watcher = watch(tempFilePath, (eventType: string) => {
          if (eventType === 'change') {
            void (async () => {
              try {
                const data = await fsPromises.readFile(tempFilePath, 'utf8');
                if (data) {
                  const response = data.trim();
                  void cleanupAndResolve(response);
                }
              } catch (readError) {
                logger.error('Error reading response file:', readError);
                void cleanupAndResolve('');
              }
            })();
          }
        });

        // Start heartbeat check interval
        heartbeatInterval = setInterval(() => {
          void (async () => {
            try {
              const stats = await fsPromises.stat(heartbeatFilePath);
              const now = Date.now();
              if (now - stats.mtime.getTime() > 3000) {
                logger.debug('Heartbeat file not updated recently. Process likely exited.');
                void cleanupAndResolve('__TIMEOUT__');
              } else {
                heartbeatFileSeen = true;
              }
            } catch (err: unknown) {
              if (err && typeof err === 'object' && 'code' in err) {
                const error = err as { code: string };
                if (error.code === 'ENOENT') {
                  if (heartbeatFileSeen) {
                    logger.debug('Heartbeat file not found after being seen. Process likely exited.');
                    void cleanupAndResolve('__TIMEOUT__');
                  } else if (Date.now() - startTime > 7000) {
                    logger.debug('Heartbeat file never appeared. Process likely failed to start.');
                    void cleanupAndResolve('__TIMEOUT__');
                  }
                } else {
                  logger.error('Heartbeat check error:', error);
                  void cleanupAndResolve('__TIMEOUT__');
                }
              } else {
                logger.error('Unexpected heartbeat check error:', err);
                void cleanupAndResolve('__TIMEOUT__');
              }
            }
          })();
        }, 1500);

        // Timeout to stop watching if no response within limit
        timeoutHandle = setTimeout(
          () => {
            logger.warn(`Input timeout reached after ${timeoutSeconds} seconds.`);
            void cleanupAndResolve('__TIMEOUT__');
          },
          timeoutSeconds * 1000 + 5000,
        );
        } catch (setupError) {
          logger.error(`Erreur lors de la configuration cmd-input pour la session ${sessionId}:`, setupError);
          await cleanupResources(
            heartbeatFilePath,
            tempFilePath,
            optionsFilePath,
            sessionId,
          );
          resolve('__TIMEOUT__');
        }
      } catch (globalError) {
        logger.error(`Erreur globale dans getCmdWindowInput pour la session ${sessionId}:`, globalError);
        try {
          await cleanupResources(
            heartbeatFilePath,
            tempFilePath,
            optionsFilePath,
            sessionId,
          );
        } catch (cleanupError) {
          logger.error(`Échec du nettoyage après erreur globale pour la session ${sessionId}:`, cleanupError);
        }
        resolve('__TIMEOUT__');
      }
    })();
  });
}
