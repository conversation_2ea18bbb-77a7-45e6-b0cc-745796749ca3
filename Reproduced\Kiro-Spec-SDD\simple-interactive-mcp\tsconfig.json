{"compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "jsx": "react-jsx"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}